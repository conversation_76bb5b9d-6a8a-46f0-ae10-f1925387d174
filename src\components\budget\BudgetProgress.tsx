'use client';

import React from 'react';
import {} from '@/components/ui/card';

interface BudgetProgressProps {
  title: string;
  allocated: number;
  spent: number;
}

export const BudgetProgress: React.FC<BudgetProgressProps> = ({ title, allocated, spent }) => {
  const progress = allocated > 0 ? Math.min((spent / allocated) * 100, 100) : 0;
  const overspent = spent > allocated;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className='flex items-center gap-4'>
      <div className='flex-1'>
        <div className='flex justify-between items-center mb-1'>
          <p className='font-medium text-sm'>{title}</p>
          <p
            className={`text-sm font-semibold ${overspent ? 'text-destructive' : 'text-muted-foreground'}`}
          >
            {formatCurrency(spent)} / {formatCurrency(allocated)}
          </p>
        </div>
        <div className='w-full bg-muted rounded-full h-2.5'>
          <div
            className={`h-2.5 rounded-full ${overspent ? 'bg-destructive' : 'bg-primary'}`}
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};
