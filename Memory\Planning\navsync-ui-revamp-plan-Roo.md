# NAVsync.io UI/UX Revamp & Feature Enhancement Plan

This document outlines a comprehensive plan to enhance the NAVsync.io user interface and experience, focusing on the Transactions, Budgeting, Charting, and Dashboard sections. The goal is to create a powerful, intuitive, and data-rich platform for financially savvy users.

---

## 1. Transactions View Revamp

### 1.1. High-Level Goal

Transform the transaction management page into a highly efficient, information-rich interface that caters to power users who appreciate data density (like <PERSON><PERSON>) on desktops, while providing a clean, accessible experience on mobile devices.

### 1.2. Proposed Solution: A Hybrid Approach

We will create a responsive component that intelligently switches between two distinct views:

*   **Desktop/Tablet View (`> 1024px`):** An interactive **Data Table** built with **TanStack Table** and styled with `shadcn/ui` to match the application's aesthetic.
*   **Mobile View (`< 1024px`):** The existing **Card View**, which is already optimized for smaller screens.

### 1.3. Data Handling Strategy

To ensure performance with large datasets, we will implement a **hybrid server-side and client-side filtering model**:

*   **Server-Side Filtering:** Major filters (date range, category) will query the database directly to fetch only relevant data subsets. This is handled by our API and is highly scalable.
*   **Client-Side Filtering:** TanStack Table will provide instantaneous filtering and sorting on the data currently loaded in the browser, giving the user a fast, responsive experience for refining results.
*   **Pagination:** Data will be fetched from the server in pages (e.g., 100 transactions at a time) to keep the client-side application light and fast.

### 1.4. Key Features & Enhancements

*   **Interactive Data Table:**
    *   **Columns:** Checkbox, Date, Merchant, Category, Amount, Account, Actions (`...`).
    *   **Functionality:** Sorting, filtering, and pagination powered by TanStack Table.
*   **Inline & Quick Categorization:**
    *   **Inline Category Selector:** The "Category" cell in the table will be an interactive dropdown, allowing for direct categorization within the row.
    *   **"Quick Assign" Icons:** A configurable set of icons for a user's most-used categories will appear in the row for one-click categorization.
*   **Retained & Enhanced Functionality:**
    *   **Inline Editing:** Beyond categorization, users will be able to edit the `merchant` and `amount` fields directly in the table.
    *   **Budget Status Indicators:** Each row will have a subtle visual indicator (e.g., a colored dot) showing the status of its category's budget (on track, nearing limit, over).
    *   **Export to CSV:** A button will allow users to export the currently filtered transaction view to a CSV file.
    *   Bulk actions and the transaction detail modal will be seamlessly integrated.

### 1.5. Component Architecture

```mermaid
graph TD
    A[TransactionsPage] --> B{TransactionView};
    B -- Is Desktop? --> C[TransactionDataTable];
    B -- Is Mobile? --> D[TransactionCardList];

    C --> E[TanStack Table Logic];
    E --> F[shadcn/ui Table Components];
    C --> G[InlineCategorySelector];
    C --> H[QuickAssignIcons];

    D --> I[TransactionCard];

    subgraph "Shared Components"
        G
        H
        J[TransactionDetailModal]
        K[BulkCategorizeModal]
    end

    C --> J;
    C --> K;
    D --> J;
    D --> K;
```

---

## 2. Budgeting Experience Enhancements

### 2.1. High-Level Goal

Evolve the budgeting feature from a simple data entry form into an intelligent, guided, and visually engaging system that helps users create and maintain realistic budgets.

### 2.2. Budget Setup Enhancements

*   **AI-Powered "Quick Start"**:
    *   Offer an AI-driven setup that analyzes the user's spending history to propose a complete, data-driven budget.
    *   **Data Prerequisite:** The system will check if the user has at least 2-3 months of categorized transaction data. If not, it will guide the user to categorize more transactions first, explaining that this is necessary for an accurate recommendation.
*   **Budget Templates**:
    *   Provide pre-built templates based on popular methodologies (e.g., 50/30/20 Rule).
*   **Guided Wizard Interface**:
    *   A multi-step process for setting up income, confirming fixed bills (auto-detected), and allocating variable spending.

### 2.3. Budget Tracking Enhancements

*   **Category Grouping**: Allow users to group categories (e.g., "Food") for a hierarchical view with collapsible sections.
*   **"Pacing" Indicators**: Add a visual marker to each progress bar showing where spending *should* be for the current day of the month, providing an early warning for over-pacing.
*   **"Sinking Funds" Feature**: A dedicated module to help users save for large, infrequent expenses (e.g., annual bills, vacations) by setting aside a fixed amount each month.
*   **Interactive "Rollover"**: At the end of the month, give users a clear, actionable choice to either roll over remaining funds to the next month's budget or reallocate them to a savings goal.
*   **Inline Editing & Reporting**:
    *   Users will be able to edit budget amounts directly in the main budget list.
    *   A feature to export "Budget vs. Actual" reports to CSV/PDF will be included.

---

## 3. Charting & Insights

### 3.1. High-Level Goal

Provide users with "legit looking," interactive, and insightful charts that empower them to analyze their financial health, track investment performance, and understand spending habits.

### 3.2. Charting Library Strategy

*   **Primary Library (`Chart.js`):** Will be used for general-purpose charts (Budgeting, Spending, Dashboard widgets) where we need flexibility and have it already integrated.
*   **Specialized Financial Library (`react-financial-charts`):** Will be integrated specifically for the investment section to provide advanced, out-of-the-box financial charts (e.g., candlestick, OHLC) and ensure a professional look and feel.

### 3.3. Proposed Charts & Insights

*   **Budgeting & Spending:**
    *   `Budget vs. Actual` (Grouped Bar Chart)
    *   `Spending Breakdown` (Interactive Donut Chart)
    *   `Spending Trend Over Time` (Line Chart)
    *   `Category Deep Dive` (Historical Bar Chart)
*   **Investment Insights:**
    *   `Portfolio NAV Over Time` (Area Chart) - The "hero" chart.
    *   `NAV vs. Benchmark Comparison` (Line Chart): An interactive chart allowing users to overlay their portfolio's NAV performance against a user-selectable stock, ETF, or market index (e.g., S&P 500).
    *   `Portfolio Allocation` (Donut/Treemap)
    *   `Dividend Income Tracker` (Bar Chart)
*   **Main Dashboard Widgets:**
    *   Mini `Budget vs. Actual` Gauge
    *   Mini `NAV Trend` Sparkline
    *   `Top 5 Spending Categories` (Horizontal Bar Chart)

---

## 4. The Main Dashboard

### 4.1. High-Level Goal

Create a central hub that serves as the user's financial command center, providing a comprehensive, scannable, and actionable overview of their entire financial world.

### 4.2. Proposed Solution: Widget-Based Layout

Build the dashboard on a responsive grid system where each key piece of information is a self-contained "widget." This architecture is clean, maintainable, and ready for future user customization.

### 4.3. Key Dashboard Widgets

1.  **Financial Overview:** Total Net Worth with Assets vs. Liabilities and a 30/90-day trend sparkline.
2.  **Monthly Budget Status:** Overall budget gauge (Spent/Budgeted/Remaining) and top 5 category progress bars.
3.  **Investment Summary:** Total investment value, day's performance, and a 30-day NAV trend sparkline.
4.  **Recent Transactions:** A compact, scrollable list of the 5-7 most recent transactions.
5.  **Upcoming Bills & Subscriptions:** A list of upcoming recurring payments to aid in cash flow planning.
6.  **Account Balances:** A concise list of all connected cash and credit accounts.
7.  **AI Financial Insights:** A card surfacing the latest 1-2 actionable insights or "Financial Wins."

### 4.4. Component Architecture

```mermaid
graph TD
    A[DashboardPage] --> B[DashboardGrid];
    B --> C[FinancialOverviewWidget];
    B --> D[BudgetStatusWidget];
    B --> E[InvestmentSummaryWidget];
    B --> F[RecentTransactionsWidget];
    B --> G[UpcomingBillsWidget];
    B --> H[AccountBalancesWidget];
    B --> I[AIInsightsWidget];

    subgraph "Widget Visualizations"
        J[NetWorthSparklineChart]
        K[BudgetGauge]
        L[NavSparklineChart]
    end

    C --> J;
    D --> K;
    E --> L;