'use client';

import React from 'react';
import { Budget } from '@/lib/services/budgetService';
import { BudgetProgress } from './BudgetProgress';
import { BudgetChart } from './BudgetChart';
import { SpendingAlert } from './SpendingAlert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface BudgetDashboardProps {
  budget: Budget;
}

export const BudgetDashboard: React.FC<BudgetDashboardProps> = ({ budget }) => {
  const overspentCategories = budget.items.filter(
    (item) => item.spent_amount > item.allocated_amount
  );

  const chartData = budget.items.map((item) => ({
    name: item.category?.name || 'Unknown',
    spent: item.spent_amount,
    allocated: item.allocated_amount,
  }));

  return (
    <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
      <div className='lg:col-span-2 space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle>Category Progress</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            {budget.items.map((item) => (
              <BudgetProgress
                key={item.id}
                title={item.category?.name || 'Unknown Category'}
                allocated={item.allocated_amount}
                spent={item.spent_amount}
              />
            ))}
          </CardContent>
        </Card>
      </div>
      <div className='space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle>Spending Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <BudgetChart data={chartData} />
          </CardContent>
        </Card>
        {overspentCategories.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Spending Alerts</CardTitle>
            </CardHeader>
            <CardContent className='space-y-2'>
              {overspentCategories.map((item) => (
                <SpendingAlert
                  key={item.id}
                  categoryName={item.category?.name || 'Unknown Category'}
                  overage={item.spent_amount - item.allocated_amount}
                />
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
