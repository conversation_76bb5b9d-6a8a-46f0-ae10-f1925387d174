import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { z } from 'zod';

const querySchema = z.object({
  period: z.enum(['current_month']),
});

type BudgetComparison = {
  category_id: string;
  category_name: string;
  allocated_amount: number;
  spent_amount: number;
  remaining_amount: number;
};

export async function GET(request: Request) {
  const supabase = await createSupabaseServerClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const queryParams = Object.fromEntries(searchParams.entries());

  const validation = querySchema.safeParse(queryParams);

  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid query parameters', details: validation.error.flatten() },
      { status: 400 }
    );
  }

  const { period } = validation.data;

  try {
    const { data, error } = await supabase.rpc('get_budget_vs_actual', {
      p_user_id: user.id,
      p_budget_period: period,
    });

    if (error) {
      console.error('Error calling get_budget_vs_actual:', error);
      return NextResponse.json({ error: 'Failed to fetch budget comparison' }, { status: 500 });
    }

    const formattedData = data.map((item: BudgetComparison) => ({
      categoryName: item.category_name,
      allocatedAmount: item.allocated_amount,
      spentAmount: item.spent_amount,
      remainingAmount: item.remaining_amount,
    }));

    return NextResponse.json({
      data: formattedData,
      metadata: {
        period,
        currency: 'USD', // Assuming USD for now
      },
    });
  } catch (error) {
    console.error('[BUDGET_COMPARISON_GET_ERROR]', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
