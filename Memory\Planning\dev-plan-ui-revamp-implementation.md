# UI/UX Revamp - Implementation Plan

This document provides a step-by-step development plan to implement the features outlined in `navsync-ui-revamp-plan-Roo.md`. It is designed to be executed sequentially, referencing existing files and detailing the creation of new ones.

---

## Phase 1: Transaction View Revamp

**Goal:** Replace the current card-based transaction list with a responsive, data-dense table view on larger screens, while retaining the card view for mobile.

### Task 1.1: Create the Transaction Data Table Component

1.  **Create New File:** `src/components/transactions/TransactionDataTable.tsx`
2.  **Install Dependency:** If not already present, add TanStack Table: `npm install @tanstack/react-table`.
3.  **Component Scaffolding:**
    *   Create a new component `TransactionDataTable`.
    *   It will accept `data` (the transactions) and `columns` as props.
    *   Use the `useReactTable` hook from TanStack Table to initialize the table instance.
4.  **Define Columns:**
    *   Create a `columns.tsx` file (`src/components/transactions/columns.tsx`) to define the table columns.
    *   **Checkbox Column:** For bulk selection.
    *   **Date Column:** Display formatted date.
    *   **Merchant Column:** Display merchant name. Add sorting functionality.
    *   **Amount Column:** Display formatted currency. Add sorting functionality.
    *   **Category Column:** This will be the most complex. For now, just display the category name. We will make it interactive in a later task.
    *   **Actions Column:** A dropdown menu (`...`) with options like "View Details" and "Split Transaction" (future).
5.  **Render the Table:**
    *   Use `shadcn/ui` components (`<Table>`, `<TableHeader>`, `<TableRow>`, `<TableHead>`, `<TableBody>`, `<TableCell>`) to render the table structure, mapping over the headers and rows provided by the `useReactTable` instance.

### Task 1.2: Integrate the Data Table into the Main List

1.  **Modify File:** `src/components/transactions/TransactionsList.tsx`
2.  **Create a Responsive Wrapper Component:**
    *   Create a new internal component, e.g., `ResponsiveTransactionView`.
    *   This component will use a custom hook (e.g., `useMediaQuery`) to check the screen width.
    *   If `width > 1024px`, it will render the new `<TransactionDataTable />`.
    *   If `width <= 1024px`, it will render the existing `TransactionCard` mapping logic.
3.  **Update `TransactionsList.tsx`:**
    *   Replace the current direct mapping of `TransactionCard` with the new `<ResponsiveTransactionView />`.
    *   Pass the `transactions` data down to it.
    *   Ensure all existing state (filters, pagination, selection) is correctly wired to both the table and card views. The `useTransactions` hook will continue to be the source of truth.

### Task 1.3: Implement Server-Side Sorting

1.  **Modify File:** `src/lib/hooks/useTransactions.ts`
    *   Add new state variables for sorting: `sorting`, `setSorting`.
    *   Update the `fetchTransactions` function to include `sortKey` and `sortOrder` parameters in the API request to `/api/transactions/get`.
2.  **Modify File:** `src/app/api/transactions/get/route.ts`
    *   Read the new `sortKey` and `sortOrder` query parameters.
    *   Update the Supabase query to use `.order(sortKey, { ascending: sortOrder === 'asc' })` to sort the results at the database level.
3.  **Modify File:** `src/components/transactions/TransactionDataTable.tsx`
    *   Pass the `sorting` state and `setSorting` function to the `useReactTable` hook.
    *   Update the column headers to be clickable buttons that trigger the sorting state change.

### Task 1.4: Implement Inline Categorization

1.  **Create New File:** `src/components/transactions/InlineCategorySelector.tsx`
    *   This component will receive a `transactionId` and the current `categoryId`.
    *   It will render the `<CategorySelector />` dropdown.
    *   On value change, it will call a server action or API endpoint to update the transaction's category directly. It should have its own loading/error state for instant feedback.
2.  **Modify File:** `src/components/transactions/columns.tsx`
    *   In the "Category" column definition, use the `cell` renderer to render the new `<InlineCategorySelector />` component for each row, passing the required props.

---

## Phase 2: Budgeting Experience Enhancements

**Goal:** Make the budgeting process more intelligent and user-friendly.

### Task 2.1: Implement AI "Quick Start" Logic

1.  **Modify File:** `src/lib/services/budgetService.ts`
    *   Create a new function `getAverageSpendingByCategory(userId, months)`.
    *   This function will query the `transactions` table for the given user over the last `months`, group by `user_category_id`, and calculate the average monthly spend for each.
2.  **Create New API Endpoint:** `src/app/api/budgets/recommendations/route.ts`
    *   This endpoint will call the new `getAverageSpendingByCategory` service function.
    *   It will first check if the user has enough historical data (e.g., > 2 months of transactions). If not, it will return an error with a helpful message.
3.  **Modify File:** `src/components/budget/BudgetWizard.tsx`
    *   In the first step, add a "Get AI Recommendation" button.
    *   When clicked, this button will call the new API endpoint.
    *   On success, it will pre-populate the budget item fields with the recommended amounts.

### Task 2.2: Implement "Pacing" Indicators

1.  **Modify File:** `src/components/budget/BudgetDashboard.tsx` (or wherever the category progress bars are rendered).
2.  **Calculate Pacing:**
    *   Get the current day of the month and the total days in the month.
    *   Calculate the pacing percentage (e.g., `currentDay / totalDays`).
3.  **Render the Indicator:**
    *   Inside the progress bar component, add a new element (e.g., a `div` with `position: absolute`).
    *   Set its `left` style property to the calculated pacing percentage. This will render a vertical line on the progress bar showing where spending "should be."

---

This concludes the initial, detailed implementation plan. We will proceed with subsequent phases (Charting, Dashboard) after these foundational elements are in place.