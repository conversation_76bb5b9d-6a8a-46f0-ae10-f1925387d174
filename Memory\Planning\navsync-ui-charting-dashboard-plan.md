# NAVsync.io UI and Charting Plan

## Overview

This document outlines a detailed plan for improving the Transactions view, Budget view and setup, Charts and Insights, and the Main Dashboard of NAVsync.io. The goal is to enhance usability, visual appeal, and functionality, catering to users familiar with spreadsheet-style tools like Tiller Money, and to evaluate the best charting library for financial data visualization.

---

## 1. Transactions View

### Goals
- Provide a clear, efficient way to view and manage transactions.
- Support viewing 20-30 transactions per page without overwhelming the user.
- Enable quick categorization with visible category icons inline.
- Maintain card view for small screens (mobile).
- Provide filtering, sorting, and search capabilities.
- Support inline editing with quick actions for recurring transactions, splits, and renaming.
- Show visual indicators for budget status per category (approaching or past budget).

### UI Options
- **Table View (Desktop and Tablet)**
  - Columns: Date, Description, Amount, Category (with icon), Account, Status.
  - Inline category icons for quick visual identification.
  - Bulk selection with checkboxes for batch actions.
  - Pagination or infinite scroll to handle large data sets.
  - Quick edit inline or modal for transaction details.
- **Card View (Mobile)**
  - Compact cards showing key info: Date, Description, Amount, Category icon.
  - Tap to expand details and edit.
- **Hybrid Approach**
  - Responsive switch between table and card views based on screen size.

### Features
- Quick categorization dropdown with most used categories and icons.
- Search by merchant, description, tags.
- Filters by date range, category, amount range.
- Sorting by date, amount, merchant.
- Export to CSV.

---

## 2. Budget View and Setup

### Goals
- Simplify budget creation and editing.
- Visualize budget allocations and spending progress clearly.
- Support monthly budgets with rollover and special expense handling.
- Allow category-based budget management linked to user categories.
- Support inline editing of budget amounts and notes.
- Show alerts and visual indicators for overspending and budget progress.

### UI Components
- **Budget Setup Wizard**
  - Guided step-by-step process.
  - Suggest budgets based on income and historical spending.
  - Templates (e.g., 50/30/20 rule).
- **Budget Overview**
  - List of budget categories with allocated and spent amounts.
  - Progress bars with color coding (green/yellow/red).
  - Alerts for overspending.
- **Budget Editing**
  - Inline editing of amounts and notes.
  - Add/remove categories.
- **Reports and Export**
  - Budget vs actual reports.
  - Export to CSV/PDF.

---

## 3. Charts and Insights

### Charting Library Evaluation

| Library                | Pros                                               | Cons                                         | Suitability for NAVsync                      |
|------------------------|---------------------------------------------------|----------------------------------------------|----------------------------------------------|
| Chart.js               | Mature, widely used, good performance, react-chartjs-2 wrapper, customizable | Requires more custom styling, limited financial chart types | Currently used, good for general charts, may need extensions for finance-specific charts |
| react-financial-charts | Dedicated financial charts (candlestick, OHLC), React components, customizable | Smaller community, less general-purpose charts | Excellent for investment charts, portfolio visualization |
| Recharts               | Easy to use, good React integration, customizable  | Larger bundle size, less financial focus     | Good for general charts, less specialized for finance |
| Victory                | Modular, customizable, React focused              | Steeper learning curve                        | Good for custom charts, moderate finance support |
| Nivo                   | Beautiful, responsive, many chart types           | Larger bundle size                            | Good for dashboards, less finance-specific |
| Syncfusion             | Enterprise-grade, many chart types including finance | Commercial license, heavier                   | Very powerful, good for finance, cost consideration |
| Highcharts             | Rich features, finance module, mature              | Commercial license, larger bundle             | Excellent finance charts, cost may be a factor |
| amCharts               | Rich features, finance charts                       | Commercial license                            | Good finance charts, cost consideration |
| AnyChart               | Comprehensive finance charts                        | Commercial license                            | Good finance charts, cost consideration |
| CanvasJS               | Lightweight, good performance                       | Less React focused                            | Suitable for simple charts, less finance focus |

### Recommendation
- Continue using **Chart.js** for general charts (budget progress, pie charts, line charts).
- Integrate **react-financial-charts** for investment-specific charts (candlestick, OHLC, portfolio allocation).
- Evaluate cost and licensing if considering commercial libraries for future enhancements.

### Chart Types to Implement
- Budget progress bars and pie charts.
- Spending trends line charts.
- Category breakdown bar/pie charts.
- Investment NAV trends (line, candlestick).
- Portfolio allocation pie charts.
- AI insights visualizations.
- User-selectable stock/ETF/index comparison charts alongside NAV.

---

## 4. Main Dashboard

### Goals
- Provide a comprehensive yet uncluttered overview.
- Prioritize key financial metrics and recent activity.
- Support customization and responsive design.
- Provide a nice default layout that most users will prefer.

### Suggested Widgets
- Financial Overview: total balances, net worth, cash flow.
- Budget Status: current month progress, alerts.
- Recent Transactions: last 20 transactions with quick categorization.
- Investment Snapshot: NAV trends, portfolio allocation, user-selectable stock/ETF/index.
- AI Insights Feed: actionable tips and alerts.
- Quick Actions: add transaction, sync accounts.

### Layout
- Responsive grid layout with collapsible sidebar.
- Mobile-first design with card stacking.
- Allow users to customize widget visibility and order (future enhancement).

---

## Mermaid Diagram: Plan Structure

```mermaid
graph TD
  A[Transactions View] --> A1[Table View with Icons]
  A --> A2[Card View for Mobile]
  B[Budget View & Setup] --> B1[Setup Wizard]
  B --> B2[Budget Overview & Editing]
  B --> B3[Reports & Export]
  C[Charts & Insights] --> C1[Chart.js for General Charts]
  C --> C2[React-Financial-Charts for Investment]
  C --> C3[Chart Types: Pie, Line, Candlestick]
  C --> C4[User-Selectable Stock/ETF/Index Comparison]
  D[Main Dashboard] --> D1[Financial Overview]
  D --> D2[Budget Status]
  D --> D3[Recent Transactions]
  D --> D4[Investment Snapshot]
  D --> D5[AI Insights Feed]
  D --> D6[Quick Actions]
```

---

## Next Steps

- Review and approve this plan.
- Discuss any preferences or constraints.
- Upon approval, create detailed UI/UX specs and implementation tasks.